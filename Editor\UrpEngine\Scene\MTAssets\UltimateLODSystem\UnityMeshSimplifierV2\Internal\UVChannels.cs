﻿using System.Runtime.CompilerServices;

namespace MTAssets.UltimateLODSystem.UnityMeshSimplifierV2.Internal
{
    internal class UVChannels<TVec>
    {
        private static readonly int UVChannelCount = MeshUtils.UVChannelCount;
        private ResizableArray<TVec>[] channels = (ResizableArray<TVec>[])null;
        private TVec[][] channelsData = (TVec[][])null;

        public TVec[][] Data
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get
            {
                for (int index = 0; index < UVChannels<TVec>.UVChannelCount; ++index)
                    this.channelsData[index] = this.channels[index] == null ? (TVec[])null : this.channels[index].Data;
                return this.channelsData;
            }
        }

        public ResizableArray<TVec> this[int index]
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get => this.channels[index];
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            set => this.channels[index] = value;
        }

        public UVChannels()
        {
            this.channels = new ResizableArray<TVec>[UVChannels<TVec>.UVChannelCount];
            this.channelsData = new TVec[UVChannels<TVec>.UVChannelCount][];
        }

        public void Resize(int capacity, bool trimExess = false)
        {
            for (int index = 0; index < UVChannels<TVec>.UVChannelCount; ++index)
            {
                if (this.channels[index] != null)
                {
                    this.channels[index].Resize(capacity, trimExess);
                }
            }
        }
    }
}
