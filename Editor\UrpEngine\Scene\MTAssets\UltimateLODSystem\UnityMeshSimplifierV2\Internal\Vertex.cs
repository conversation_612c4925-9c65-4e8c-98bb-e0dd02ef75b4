﻿using System;
using System.Runtime.CompilerServices;

namespace MTAssets.UltimateLODSystem.UnityMeshSimplifierV2.Internal
{
    internal struct Vertex : IEquatable<Vertex>
    {
        public int index;
        public Vector3d p;
        public int tstart;
        public int tcount;
        public SymmetricMatrix q;
        public bool borderEdge;
        public bool uvSeamEdge;
        public bool uvFoldoverEdge;

        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public Vertex(int index, Vector3d p)
        {
            this.index = index;
            this.p = p;
            this.tstart = 0;
            this.tcount = 0;
            this.q = new SymmetricMatrix();
            this.borderEdge = true;
            this.uvSeamEdge = false;
            this.uvFoldoverEdge = false;
        }

        public override int GetHashCode() => this.index;

        public override bool Equals(object obj) => obj is Vertex vertex && this.index == vertex.index;

        public bool Equals(Vertex other) => this.index == other.index;
    }
}
