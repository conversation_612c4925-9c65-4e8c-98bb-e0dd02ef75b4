﻿using System.Runtime.CompilerServices;
using UnityEngine;

namespace MTAssets.UltimateLODSystem.UnityMeshSimplifierV2.Internal
{
    internal class BlendShapeContainer
    {
        private readonly string shapeName;
        private readonly BlendShapeFrameContainer[] frames;

        public BlendShapeContainer(BlendShape blendShape)
        {
            this.shapeName = blendShape.ShapeName;
            this.frames = new BlendShapeFrameContainer[blendShape.Frames.Length];
            for (int index = 0; index < this.frames.Length; ++index)
            {
                this.frames[index] = new BlendShapeFrameContainer(blendShape.Frames[index]);
            }
        }

        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public void MoveVertexElement(int dst, int src)
        {
            for (int index = 0; index < this.frames.Length; ++index)
            {
                this.frames[index].MoveVertexElement(dst, src);
            }
        }

        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public void InterpolateVertexAttributes(int dst, int i0, int i1, int i2, ref Vector3 barycentricCoord)
        {
            for (int index = 0; index < this.frames.Length; ++index)
            {
                this.frames[index].InterpolateVertexAttributes(dst, i0, i1, i2, ref barycentricCoord);
            }
        }

        public void Resize(int length, bool trimExess = false)
        {
            for (int index = 0; index < this.frames.Length; ++index)
            {
                this.frames[index].Resize(length, trimExess);
            }
        }

        public BlendShape ToBlendShape()
        {
            BlendShapeFrame[] frames = new BlendShapeFrame[this.frames.Length];
            for (int index = 0; index < frames.Length; ++index)
            {
                frames[index] = this.frames[index].ToBlendShapeFrame();
            }
            return new BlendShape(this.shapeName, frames);
        }
    }
}
