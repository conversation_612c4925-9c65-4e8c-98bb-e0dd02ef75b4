﻿using System;
using System.Runtime.CompilerServices;

namespace MTAssets.UltimateLODSystem.UnityMeshSimplifierV2
{
    internal sealed class ResizableArray<T>
    {
        private T[] items = (T[])null;
        private int length = 0;
        private static T[] emptyArr = new T[0];

        public int Length
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get => this.length;
        }

        public T[] Data
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get => this.items;
        }

        public T this[int index]
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get => this.items[index];
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            set => this.items[index] = value;
        }

        public ResizableArray(int capacity)
          : this(capacity, 0)
        {
        }

        public ResizableArray(int capacity, int length)
        {
            if (capacity < 0)
                throw new ArgumentOutOfRangeException(nameof(capacity));
            if (length < 0 || length > capacity)
                throw new ArgumentOutOfRangeException(nameof(length));
            this.items = capacity <= 0 ? ResizableArray<T>.emptyArr : new T[capacity];
            this.length = length;
        }

        public ResizableArray(T[] initialArray)
        {
            if (initialArray == null)
                throw new ArgumentNullException(nameof(initialArray));
            if (initialArray.Length != 0)
            {
                this.items = new T[initialArray.Length];
                this.length = initialArray.Length;
                Array.Copy((Array)initialArray, 0, (Array)this.items, 0, initialArray.Length);
            }
            else
            {
                this.items = ResizableArray<T>.emptyArr;
                this.length = 0;
            }
        }

        private void IncreaseCapacity(int capacity)
        {
            T[] destinationArray = new T[capacity];
            Array.Copy((Array)this.items, 0, (Array)destinationArray, 0, Math.Min(this.length, capacity));
            this.items = destinationArray;
        }

        public void Clear()
        {
            Array.Clear((Array)this.items, 0, this.length);
            this.length = 0;
        }

        public void Resize(int length, bool trimExess = false, bool clearMemory = false)
        {
            if (length < 0)
                throw new ArgumentOutOfRangeException(nameof(length));
            if (length > this.items.Length)
                this.IncreaseCapacity(length);
            else if (length < this.length & clearMemory)
                Array.Clear((Array)this.items, length, this.length - length);
            this.length = length;
            if (!trimExess)
                return;
            this.TrimExcess();
        }

        public void TrimExcess()
        {
            if (this.items.Length == this.length)
                return;
            T[] destinationArray = new T[this.length];
            Array.Copy((Array)this.items, 0, (Array)destinationArray, 0, this.length);
            this.items = destinationArray;
        }

        public void Add(T item)
        {
            if (this.length >= this.items.Length)
                this.IncreaseCapacity(this.items.Length << 1);
            this.items[this.length++] = item;
        }

        public T[] ToArray()
        {
            T[] destinationArray = new T[this.length];
            Array.Copy((Array)this.items, 0, (Array)destinationArray, 0, this.length);
            return destinationArray;
        }
    }
}
