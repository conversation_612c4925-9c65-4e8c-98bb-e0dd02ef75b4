using System;
using System.Collections;
using System.Collections.Generic;
using System.IO;
using UnityEditor;
using UnityEditor.SceneManagement;
using UnityEngine;
using UnityEngine.SceneManagement;

namespace SceneEnvironment
{
    [CustomEditor(typeof(EnvironmentController))]
    public class EnvironmentControllerEditor : Editor
    {
        public SerializedProperty layer;
        public SerializedProperty priority;
        public SerializedProperty environmentData;
        public SerializedProperty characterData;

        private void OnEnable()
        {
            layer = serializedObject.FindProperty("layer");
            priority = serializedObject.FindProperty("priority");
            environmentData = serializedObject.FindProperty("m_EnvironmentData");
            characterData = serializedObject.FindProperty("m_CharacterData");
        }

        public override void OnInspectorGUI()
        {
            Undo.RecordObject(target, "EnvironmentCtrl");
            
            EditorGUI.BeginChangeCheck();

            EditorGUILayout.PropertyField(layer, new GUIContent("层级"));
            EditorGUILayout.PropertyField(priority, new GUIContent("优先级"));
            
            EditorGUILayout.Space();

            /////////////////////////////////////////////////////////////////////////////////////
            /// 环境
            EditorGUILayout.BeginVertical(EditorStyles.helpBox);
            
            environmentData.objectReferenceValue = EditorGUILayout.ObjectField(new GUIContent("环境数据"),
                environmentData.objectReferenceValue, typeof(EnvironmentData));
            if (environmentData.objectReferenceValue == null)
            {
                if (GUILayout.Button("创建环境数据"))
                {
                    environmentData.objectReferenceValue = CreateDefaultEnvironmentData();
                    serializedObject.ApplyModifiedProperties();
                }
            }
            else
            {
                EditorGUILayout.Space();
                Undo.RecordObject(environmentData.objectReferenceValue, "EnvironmentData");
                EditorGUI.BeginChangeCheck();
                EnvironmentDataEditor.DoEnvironmentGUI(target as EnvironmentController, environmentData.objectReferenceValue as EnvironmentData);
                if (EditorGUI.EndChangeCheck())
                {
                    EditorUtility.SetDirty(environmentData.objectReferenceValue);
                    if (!Application.isPlaying)
                        EditorSceneManager.MarkAllScenesDirty();
                }
            }
            
            EditorGUILayout.EndVertical();
            
            /////////////////////////////////////////////////////////////////////////////////////
            
            EditorGUILayout.Space();
            
            /////////////////////////////////////////////////////////////////////////////////////
            /// 角色

            EditorGUILayout.BeginVertical(EditorStyles.helpBox);
            
            characterData.objectReferenceValue = EditorGUILayout.ObjectField(new GUIContent("角色数据"),
                characterData.objectReferenceValue, typeof(CharacterData));
            if (characterData.objectReferenceValue == null)
            {
                if (GUILayout.Button("创建角色数据"))
                {
                    characterData.objectReferenceValue = CreateDefaultCharacterData();
                    serializedObject.ApplyModifiedProperties();
                }
            }
            else
            {
                EditorGUILayout.Space();
                Undo.RecordObject(characterData.objectReferenceValue, "CharacterData");
                EditorGUI.BeginChangeCheck();
                EnvironmentDataEditor.DoCharacterUI(target as EnvironmentController, characterData.objectReferenceValue as CharacterData);
                if (EditorGUI.EndChangeCheck())
                {
                    EditorUtility.SetDirty(characterData.objectReferenceValue);
                    if (!Application.isPlaying)
                        EditorSceneManager.MarkAllScenesDirty();
                }
            }
            
            EditorGUILayout.EndVertical();
            
            /////////////////////////////////////////////////////////////////////////////////////

            if (EditorGUI.EndChangeCheck())
            {
                serializedObject.ApplyModifiedProperties();
            }
        }

        private EnvironmentData CreateDefaultEnvironmentData()
        {
            EnvironmentData data = new EnvironmentData();
            Scene scene = EditorSceneManager.GetActiveScene();
            string dir = $"{Path.GetDirectoryName(scene.path)}/{Path.GetFileNameWithoutExtension(scene.name)}/";
            if (!Directory.Exists(dir))
            {
                Directory.CreateDirectory(dir);
            }

            string file = dir + "EnvironmentData.asset";
            if (File.Exists(file))
            {
                AssetDatabase.DeleteAsset(file);
            }

            AssetDatabase.CreateAsset(data, file);
            AssetDatabase.Refresh();
            return data;
        }
        
        private CharacterData CreateDefaultCharacterData()
        {
            CharacterData data = new CharacterData();
            Scene scene = EditorSceneManager.GetActiveScene();
            string dir = $"{Path.GetDirectoryName(scene.path)}/{Path.GetFileNameWithoutExtension(scene.name)}/";
            if (!Directory.Exists(dir))
            {
                Directory.CreateDirectory(dir);
            }

            string file = dir + "CharacterData.asset";
            if (File.Exists(file))
            {
                AssetDatabase.DeleteAsset(file);
            }

            AssetDatabase.CreateAsset(data, file);
            AssetDatabase.Refresh();
            return data;
        }
        
    }
}