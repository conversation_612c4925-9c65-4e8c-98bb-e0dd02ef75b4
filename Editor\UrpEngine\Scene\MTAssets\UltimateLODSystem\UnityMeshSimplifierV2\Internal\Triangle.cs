﻿using System;
using System.Runtime.CompilerServices;

namespace MTAssets.UltimateLODSystem.UnityMeshSimplifierV2.Internal
{
    internal struct Triangle : IEquatable<Triangle>
    {
        public int index;
        public int v0;
        public int v1;
        public int v2;
        public int subMeshIndex;
        public int va0;
        public int va1;
        public int va2;
        public double err0;
        public double err1;
        public double err2;
        public double err3;
        public bool deleted;
        public bool dirty;
        public Vector3d n;

        public int this[int index]
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get
            {
                int num;
                switch (index)
                {
                    case 0:
                        num = this.v0;
                        break;
                    case 1:
                        num = this.v1;
                        break;
                    default:
                        num = this.v2;
                        break;
                }
                return num;
            }
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            set
            {
                switch (index)
                {
                    case 0:
                        this.v0 = value;
                        break;
                    case 1:
                        this.v1 = value;
                        break;
                    case 2:
                        this.v2 = value;
                        break;
                    default:
                        throw new ArgumentOutOfRangeException(nameof(index));
                }
            }
        }

        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public Triangle(int index, int v0, int v1, int v2, int subMeshIndex)
        {
            this.index = index;
            this.v0 = v0;
            this.v1 = v1;
            this.v2 = v2;
            this.subMeshIndex = subMeshIndex;
            this.va0 = v0;
            this.va1 = v1;
            this.va2 = v2;
            this.err0 = this.err1 = this.err2 = this.err3 = 0.0;
            this.deleted = this.dirty = false;
            this.n = new Vector3d();
        }

        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public void GetAttributeIndices(int[] attributeIndices)
        {
            attributeIndices[0] = this.va0;
            attributeIndices[1] = this.va1;
            attributeIndices[2] = this.va2;
        }

        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public void SetAttributeIndex(int index, int value)
        {
            switch (index)
            {
                case 0:
                    this.va0 = value;
                    break;
                case 1:
                    this.va1 = value;
                    break;
                case 2:
                    this.va2 = value;
                    break;
                default:
                    throw new ArgumentOutOfRangeException(nameof(index));
            }
        }

        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public void GetErrors(double[] err)
        {
            err[0] = this.err0;
            err[1] = this.err1;
            err[2] = this.err2;
        }

        public override int GetHashCode() => this.index;

        public override bool Equals(object obj) => obj is Triangle triangle && this.index == triangle.index;

        public bool Equals(Triangle other) => this.index == other.index;
    }
}
