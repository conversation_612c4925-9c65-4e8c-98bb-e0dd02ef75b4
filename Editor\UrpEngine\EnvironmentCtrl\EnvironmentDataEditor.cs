using System;
using System.Collections;
using System.Collections.Generic;
using System.Reflection;
using SceneEnvironment;
using UnityEditor;
using UnityEngine;
using UnityEngine.Rendering;

public class EnvironmentDataEditor
{
    public static void DoEnvironmentGUI(EnvironmentController ctrl,EnvironmentData data)
    {
        data.lightmapColor = EditorGUILayout.ColorField(new GUIContent("环境光颜色"), data.lightmapColor, true, false, true);
        data.reflectionColor = EditorGUILayout.ColorField(new GUIContent("反射颜色"), data.reflectionColor, true, false, true);
        data.plantLightColor = EditorGUILayout.ColorField(new GUIContent("植被灯光颜色"), data.plantLightColor, true, false, true);
        data.plantEnvColor = EditorGUILayout.ColorField(new GUIContent("植被环境颜色"), data.plantEnvColor, true, false, true);
        data.maxMetallic = EditorGUILayout.Slider(new GUIContent("金属度限制"), data.maxMetallic, 0, 1);
        data.maxOcclusion = EditorGUILayout.Slider(new GUIContent("AO限制"), data.maxOcclusion, 0, 1);
        data.emissionInstensity = EditorGUILayout.Slider(new GUIContent("自发光强度"), data.emissionInstensity, 0, 1);
        data.bakedLightInstensity = EditorGUILayout.FloatField(new GUIContent("烘焙光强度"), data.bakedLightInstensity);
        data.shadowIntensity = EditorGUILayout.Slider(new GUIContent("阴影强度"), data.shadowIntensity, 0, 1);
        data.reflectionTex = EditorGUILayout.ObjectField(new GUIContent("反射贴图"), data.reflectionTex, typeof(Cubemap)) as Cubemap;
        
        /////////////////////////////////////////////////////////////////////////////////////
        
        EditorGUILayout.Space();

        /////////////////////////////////////////////////////////////////////////////////////
        // 天气 
        
        EditorGUILayout.LabelField("天气", EditorStyles.boldLabel);
        EditorGUI.indentLevel++;
        DrawRainGUI(data);
        EditorGUILayout.Space();
        GUI.enabled = true;
        EditorGUI.indentLevel--;
        /////////////////////////////////////////////////////////////////////////////////////
        // 环境 
        
        EditorGUILayout.LabelField("环境", EditorStyles.boldLabel);
        EditorGUI.indentLevel++;
        DrawCausticsGUI(data);
        EditorGUILayout.Space();
        GUI.enabled = true;
        EditorGUI.indentLevel--;
        /////////////////////////////////////////////////////////////////////////////////////
    }

    public static void DoCharacterUI(EnvironmentController ctrl,CharacterData data)
    {
        data.avatarLightDirType =
            (AvatarLightDirType)EditorGUILayout.EnumPopup(new GUIContent("方向类型"), data.avatarLightDirType);
        if (data.avatarLightDirType == AvatarLightDirType.Camera)
        {
            data.avatarLightDirWithCamera =
                EditorGUILayout.Vector4Field(new GUIContent("方向"), data.avatarLightDirWithCamera);
        }

        data.avatarLightColor =
            EditorGUILayout.ColorField(new GUIContent("灯光颜色"), data.avatarLightColor, true, false, true);
        data.avatarSkinLightColor =
            EditorGUILayout.ColorField(new GUIContent("皮肤灯光颜色"), data.avatarSkinLightColor, true, false, true);
        data.avatarHairLightColor =
            EditorGUILayout.ColorField(new GUIContent("头发灯光颜色"), data.avatarHairLightColor, true, false, true);
        data.avatarLightIntensity = EditorGUILayout.FloatField(new GUIContent("灯光强度"), data.avatarLightIntensity);
        data.avatarAmbientColor =
            EditorGUILayout.ColorField(new GUIContent("环境光颜色"), data.avatarAmbientColor, true, false, true);
        data.avatarReflectColor =
            EditorGUILayout.ColorField(new GUIContent("环境反射颜色"), data.avatarReflectColor, true, true, true);
        data.avatarHairAmbientColor =
            EditorGUILayout.ColorField(new GUIContent("头发反射颜色"), data.avatarHairAmbientColor, true, false, true);

        data.avatarReflectTex =
            EditorGUILayout.ObjectField(new GUIContent("反射贴图"), data.avatarReflectTex, typeof(Cubemap)) as Cubemap;

        //没上反射贴图自动上默认贴图
        if (data.avatarReflectTex == null)
        {
            data.avatarReflectTex = AssetDatabase.LoadAssetAtPath<Cubemap>(
                "Assets/Game/Environments/Shared/Texture/RoleReflectTex.hdr");
            EditorUtility.SetDirty(data);
        }
    }

    private static void DrawRainGUI(EnvironmentData data)
    {
        data.enableRain = EditorGUILayout.Toggle(new GUIContent("雨天"), data.enableRain);
        if (data.enableRain)
        {
            data.rainIntensity = EditorGUILayout.Slider(new GUIContent("强度"), data.rainIntensity, 0, 1);
            data.rainTex =
                EditorGUILayout.ObjectField(new GUIContent("法线"), data.rainTex, typeof(Texture2D)) as Texture2D;
            data.rainSurfaceWaterDensity =
                EditorGUILayout.FloatField(new GUIContent("密度"), data.rainSurfaceWaterDensity);
            data.rainSurfaceWaterSpeed = EditorGUILayout.Vector4Field(new GUIContent("速度"), data.rainSurfaceWaterSpeed);
            data.rainSurfaceRoughness =
                EditorGUILayout.Slider(new GUIContent("表面粗糙度"), data.rainSurfaceRoughness, 0.0F, 1.0F);
            data.enableRainRipple = EditorGUILayout.Toggle(new GUIContent("涟漪"), data.enableRainRipple);
            if (data.enableRainRipple)
            {
                EditorGUI.indentLevel++;
                data.rainRippleTex = EditorGUILayout.ObjectField(new GUIContent("法线"), data.rainRippleTex, typeof(Texture2D)) as Texture2D;
                data.rainRippleDensity = EditorGUILayout.FloatField(new GUIContent("密度"), data.rainRippleDensity);
                data.rainRippleFrequency = EditorGUILayout.FloatField(new GUIContent("频率"), data.rainRippleFrequency);
                data.rainRippleIntensity = EditorGUILayout.FloatField(new GUIContent("法线强度"), data.rainRippleIntensity);
                EditorGUI.indentLevel--;
            }
        }
    }
    
    private static void DrawCausticsGUI(EnvironmentData data)
    {
        data.enableCaustics = EditorGUILayout.Toggle(new GUIContent("阳光焦散"), data.enableCaustics);
        if (data.enableCaustics)
        {
            data.causticsTex = EditorGUILayout.ObjectField(new GUIContent("焦散贴图"), data.causticsTex, typeof(Texture2D)) as Texture2D;
            data.causticsColor = EditorGUILayout.ColorField(new GUIContent("颜色"), data.causticsColor ,true, true, true);
            data.causticsDistance = EditorGUILayout.Slider(new GUIContent("距离"), data.causticsDistance, 1f, 100f);
            data.causticsFadeFactor = EditorGUILayout.Slider(new GUIContent("淡出"), data.causticsFadeFactor, 0.01f, 3f);
            data.causticsSpeedTilling1 = EditorGUILayout.Vector4Field(new GUIContent("速度/Tilling 1"), data.causticsSpeedTilling1);
            data.causticsSpeedTilling2 = EditorGUILayout.Vector4Field(new GUIContent("速度/Tilling 2"), data.causticsSpeedTilling2);
        }
    }

    private static Vector4[] GenerateSH(Cubemap cubemap)
    {
        var TextureAssetImporter =
            Type.GetType(
                "TextureAssetImporter,Assembly-CSharp-Editor, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null");
        var BeginTextureEditing =
            TextureAssetImporter.GetMethod("BeginTextureEditing", BindingFlags.Public | BindingFlags.Static);
        var EndTextureEditing =
            TextureAssetImporter.GetMethod("EndTextureEditing", BindingFlags.Public | BindingFlags.Static);

        BeginTextureEditing.Invoke(null, null);
        Vector4[] coefficients = new Vector4[7];
        CubemapSHProjector.CheckAndConvertEnvMap(ref cubemap, ref coefficients);
        EndTextureEditing.Invoke(null, null);
        return coefficients;
    }
}