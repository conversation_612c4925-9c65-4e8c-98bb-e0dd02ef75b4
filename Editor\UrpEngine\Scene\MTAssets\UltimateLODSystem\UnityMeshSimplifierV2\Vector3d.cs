﻿using System;
using System.Runtime.CompilerServices;
using UnityEngine;

namespace MTAssets.UltimateLODSystem.UnityMeshSimplifierV2
{
    public struct Vector3d : IEquatable<Vector3d>
    {
        public static readonly Vector3d zero = new Vector3d(0.0, 0.0, 0.0);
        public const double Epsilon = 4.94065645841247E-324;
        public double x;
        public double y;
        public double z;

        public double Magnitude
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get => Math.Sqrt(this.x * this.x + this.y * this.y + this.z * this.z);
        }

        public double MagnitudeSqr
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get => this.x * this.x + this.y * this.y + this.z * this.z;
        }

        public Vector3d Normalized
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get
            {
                Vector3d result;
                Vector3d.Normalize(ref this, out result);
                return result;
            }
        }

        public double this[int index]
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get
            {
                switch (index)
                {
                    case 0:
                        return this.x;
                    case 1:
                        return this.y;
                    case 2:
                        return this.z;
                    default:
                        throw new ArgumentOutOfRangeException(nameof(index), "Invalid Vector3d index!");
                }
            }
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            set
            {
                switch (index)
                {
                    case 0:
                        this.x = value;
                        break;
                    case 1:
                        this.y = value;
                        break;
                    case 2:
                        this.z = value;
                        break;
                    default:
                        throw new ArgumentOutOfRangeException(nameof(index), "Invalid Vector3d index!");
                }
            }
        }

        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public Vector3d(double value)
        {
            this.x = value;
            this.y = value;
            this.z = value;
        }

        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public Vector3d(double x, double y, double z)
        {
            this.x = x;
            this.y = y;
            this.z = z;
        }

        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public Vector3d(Vector3 vector)
        {
            this.x = (double)vector.x;
            this.y = (double)vector.y;
            this.z = (double)vector.z;
        }

        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static Vector3d operator +(Vector3d a, Vector3d b) => new Vector3d(a.x + b.x, a.y + b.y, a.z + b.z);

        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static Vector3d operator -(Vector3d a, Vector3d b) => new Vector3d(a.x - b.x, a.y - b.y, a.z - b.z);

        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static Vector3d operator *(Vector3d a, double d) => new Vector3d(a.x * d, a.y * d, a.z * d);

        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static Vector3d operator *(double d, Vector3d a) => new Vector3d(a.x * d, a.y * d, a.z * d);

        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static Vector3d operator /(Vector3d a, double d) => new Vector3d(a.x / d, a.y / d, a.z / d);

        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static Vector3d operator -(Vector3d a) => new Vector3d(-a.x, -a.y, -a.z);

        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static bool operator ==(Vector3d lhs, Vector3d rhs) => (lhs - rhs).MagnitudeSqr < double.Epsilon;

        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static bool operator !=(Vector3d lhs, Vector3d rhs) => (lhs - rhs).MagnitudeSqr >= double.Epsilon;

        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static implicit operator Vector3d(Vector3 v) => new Vector3d((double)v.x, (double)v.y, (double)v.z);

        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static explicit operator Vector3(Vector3d v) => new Vector3((float)v.x, (float)v.y, (float)v.z);

        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public void Set(double x, double y, double z)
        {
            this.x = x;
            this.y = y;
            this.z = z;
        }

        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public void Scale(ref Vector3d scale)
        {
            this.x *= scale.x;
            this.y *= scale.y;
            this.z *= scale.z;
        }

        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public void Normalize()
        {
            double magnitude = this.Magnitude;
            if (magnitude > double.Epsilon)
            {
                this.x /= magnitude;
                this.y /= magnitude;
                this.z /= magnitude;
            }
            else
                this.x = this.y = this.z = 0.0;
        }

        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public void Clamp(double min, double max)
        {
            if (this.x < min)
                this.x = min;
            else if (this.x > max)
                this.x = max;
            if (this.y < min)
                this.y = min;
            else if (this.y > max)
                this.y = max;
            if (this.z < min)
            {
                this.z = min;
            }
            else
            {
                if (this.z <= max)
                    return;
                this.z = max;
            }
        }

        public override int GetHashCode() => this.x.GetHashCode() ^ this.y.GetHashCode() << 2 ^ this.z.GetHashCode() >> 2;

        public override bool Equals(object obj) => obj is Vector3d vector3d && this.x == vector3d.x && this.y == vector3d.y && this.z == vector3d.z;

        public bool Equals(Vector3d other) => this.x == other.x && this.y == other.y && this.z == other.z;

        public override string ToString() => string.Format("({0:F1}, {1:F1}, {2:F1})", (object)this.x, (object)this.y, (object)this.z);

        public string ToString(string format) => string.Format("({0}, {1}, {2})", (object)this.x.ToString(format), (object)this.y.ToString(format), (object)this.z.ToString(format));

        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static double Dot(ref Vector3d lhs, ref Vector3d rhs) => lhs.x * rhs.x + lhs.y * rhs.y + lhs.z * rhs.z;

        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static void Cross(ref Vector3d lhs, ref Vector3d rhs, out Vector3d result) => result = new Vector3d(lhs.y * rhs.z - lhs.z * rhs.y, lhs.z * rhs.x - lhs.x * rhs.z, lhs.x * rhs.y - lhs.y * rhs.x);

        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static double Angle(ref Vector3d from, ref Vector3d to)
        {
            Vector3d normalized1 = from.Normalized;
            Vector3d normalized2 = to.Normalized;
            return Math.Acos(MathHelper.Clamp(Vector3d.Dot(ref normalized1, ref normalized2), -1.0, 1.0)) * (180.0 / Math.PI);
        }

        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static void Lerp(ref Vector3d a, ref Vector3d b, double t, out Vector3d result) => result = new Vector3d(a.x + (b.x - a.x) * t, a.y + (b.y - a.y) * t, a.z + (b.z - a.z) * t);

        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static void Scale(ref Vector3d a, ref Vector3d b, out Vector3d result) => result = new Vector3d(a.x * b.x, a.y * b.y, a.z * b.z);

        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static void Normalize(ref Vector3d value, out Vector3d result)
        {
            double magnitude = value.Magnitude;
            if (magnitude > double.Epsilon)
                result = new Vector3d(value.x / magnitude, value.y / magnitude, value.z / magnitude);
            else
                result = Vector3d.zero;
        }
    }
}
