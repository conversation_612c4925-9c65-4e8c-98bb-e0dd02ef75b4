﻿using System.Runtime.CompilerServices;
using UnityEngine;

namespace MTAssets.UltimateLODSystem.UnityMeshSimplifierV2.Internal
{
    internal class BlendShapeFrameContainer
    {
        private readonly float frameWeight;
        private readonly ResizableArray<Vector3> deltaVertices;
        private readonly ResizableArray<Vector3> deltaNormals;
        private readonly ResizableArray<Vector3> deltaTangents;

        public BlendShapeFrameContainer(BlendShapeFrame frame)
        {
            this.frameWeight = frame.FrameWeight;
            this.deltaVertices = new ResizableArray<Vector3>(frame.DeltaVertices);
            this.deltaNormals = new ResizableArray<Vector3>(frame.DeltaNormals);
            this.deltaTangents = new ResizableArray<Vector3>(frame.DeltaTangents);
        }

        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public void MoveVertexElement(int dst, int src)
        {
            this.deltaVertices[dst] = this.deltaVertices[src];
            this.deltaNormals[dst] = this.deltaNormals[src];
            this.deltaTangents[dst] = this.deltaTangents[src];
        }

        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public void InterpolateVertexAttributes(int dst, int i0, int i1, int i2, ref Vector3 barycentricCoord)
        {
            this.deltaVertices[dst] = this.deltaVertices[i0] * barycentricCoord.x + this.deltaVertices[i1] * barycentricCoord.y + this.deltaVertices[i2] * barycentricCoord.z;
            this.deltaNormals[dst] = Vector3.Normalize(this.deltaNormals[i0] * barycentricCoord.x + this.deltaNormals[i1] * barycentricCoord.y + this.deltaNormals[i2] * barycentricCoord.z);
            this.deltaTangents[dst] = Vector3.Normalize(this.deltaTangents[i0] * barycentricCoord.x + this.deltaTangents[i1] * barycentricCoord.y + this.deltaTangents[i2] * barycentricCoord.z);
        }

        public void Resize(int length, bool trimExess = false)
        {
            this.deltaVertices.Resize(length, trimExess);
            this.deltaNormals.Resize(length, trimExess);
            this.deltaTangents.Resize(length, trimExess);
        }

        public BlendShapeFrame ToBlendShapeFrame() => new BlendShapeFrame(this.frameWeight, this.deltaVertices.ToArray(), this.deltaNormals.ToArray(), this.deltaTangents.ToArray());
    }
}
