﻿using System;
using System.Runtime.CompilerServices;

namespace MTAssets.UltimateLODSystem.UnityMeshSimplifierV2
{
    public struct SymmetricMatrix
    {
        public double m0;
        public double m1;
        public double m2;
        public double m3;
        public double m4;
        public double m5;
        public double m6;
        public double m7;
        public double m8;
        public double m9;

        public double this[int index]
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get
            {
                switch (index)
                {
                    case 0:
                        return this.m0;
                    case 1:
                        return this.m1;
                    case 2:
                        return this.m2;
                    case 3:
                        return this.m3;
                    case 4:
                        return this.m4;
                    case 5:
                        return this.m5;
                    case 6:
                        return this.m6;
                    case 7:
                        return this.m7;
                    case 8:
                        return this.m8;
                    case 9:
                        return this.m9;
                    default:
                        throw new ArgumentOutOfRangeException(nameof(index));
                }
            }
        }

        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public SymmetricMatrix(double c)
        {
            this.m0 = c;
            this.m1 = c;
            this.m2 = c;
            this.m3 = c;
            this.m4 = c;
            this.m5 = c;
            this.m6 = c;
            this.m7 = c;
            this.m8 = c;
            this.m9 = c;
        }

        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public SymmetricMatrix(
          double m0,
          double m1,
          double m2,
          double m3,
          double m4,
          double m5,
          double m6,
          double m7,
          double m8,
          double m9)
        {
            this.m0 = m0;
            this.m1 = m1;
            this.m2 = m2;
            this.m3 = m3;
            this.m4 = m4;
            this.m5 = m5;
            this.m6 = m6;
            this.m7 = m7;
            this.m8 = m8;
            this.m9 = m9;
        }

        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public SymmetricMatrix(double a, double b, double c, double d)
        {
            this.m0 = a * a;
            this.m1 = a * b;
            this.m2 = a * c;
            this.m3 = a * d;
            this.m4 = b * b;
            this.m5 = b * c;
            this.m6 = b * d;
            this.m7 = c * c;
            this.m8 = c * d;
            this.m9 = d * d;
        }

        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static SymmetricMatrix operator +(SymmetricMatrix a, SymmetricMatrix b) => new SymmetricMatrix(a.m0 + b.m0, a.m1 + b.m1, a.m2 + b.m2, a.m3 + b.m3, a.m4 + b.m4, a.m5 + b.m5, a.m6 + b.m6, a.m7 + b.m7, a.m8 + b.m8, a.m9 + b.m9);

        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        internal double Determinant1() => this.m0 * this.m4 * this.m7 + this.m2 * this.m1 * this.m5 + this.m1 * this.m5 * this.m2 - this.m2 * this.m4 * this.m2 - this.m0 * this.m5 * this.m5 - this.m1 * this.m1 * this.m7;

        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        internal double Determinant2() => this.m1 * this.m5 * this.m8 + this.m3 * this.m4 * this.m7 + this.m2 * this.m6 * this.m5 - this.m3 * this.m5 * this.m5 - this.m1 * this.m6 * this.m7 - this.m2 * this.m4 * this.m8;

        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        internal double Determinant3() => this.m0 * this.m5 * this.m8 + this.m3 * this.m1 * this.m7 + this.m2 * this.m6 * this.m2 - this.m3 * this.m5 * this.m2 - this.m0 * this.m6 * this.m7 - this.m2 * this.m1 * this.m8;

        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        internal double Determinant4() => this.m0 * this.m4 * this.m8 + this.m3 * this.m1 * this.m5 + this.m1 * this.m6 * this.m2 - this.m3 * this.m4 * this.m2 - this.m0 * this.m6 * this.m5 - this.m1 * this.m1 * this.m8;

        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public double Determinant(
          int a11,
          int a12,
          int a13,
          int a21,
          int a22,
          int a23,
          int a31,
          int a32,
          int a33)
        {
            return this[a11] * this[a22] * this[a33] + this[a13] * this[a21] * this[a32] + this[a12] * this[a23] * this[a31] - this[a13] * this[a22] * this[a31] - this[a11] * this[a23] * this[a32] - this[a12] * this[a21] * this[a33];
        }
    }
}
