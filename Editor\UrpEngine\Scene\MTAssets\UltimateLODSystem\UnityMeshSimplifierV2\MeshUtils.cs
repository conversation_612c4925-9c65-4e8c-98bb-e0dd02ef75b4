﻿using System;
using System.Collections.Generic;
using UnityEngine;

namespace MTAssets.UltimateLODSystem.UnityMeshSimplifierV2
{
    public static class MeshUtils
    {
        public static readonly int UVChannelCount = 4;

        public static Mesh CreateMesh(
          Vector3[] vertices,
          int[][] indices,
          Vector3[] normals,
          Vector4[] tangents,
          Color[] colors,
          BoneWeight[] boneWeights,
          List<Vector2>[] uvs,
          Matrix4x4[] bindposes,
          BlendShape[] blendShapes)
        {
            return MeshUtils.CreateMesh(vertices, indices, normals, tangents, colors, boneWeights, uvs, (List<Vector3>[])null, (List<Vector4>[])null, bindposes, blendShapes);
        }

        public static Mesh CreateMesh(
          Vector3[] vertices,
          int[][] indices,
          Vector3[] normals,
          Vector4[] tangents,
          Color[] colors,
          BoneWeight[] boneWeights,
          List<Vector4>[] uvs,
          Matrix4x4[] bindposes,
          BlendShape[] blendShapes)
        {
            return MeshUtils.CreateMesh(vertices, indices, normals, tangents, colors, boneWeights, (List<Vector2>[])null, (List<Vector3>[])null, uvs, bindposes, blendShapes);
        }

        public static Mesh CreateMesh(
          Vector3[] vertices,
          int[][] indices,
          Vector3[] normals,
          Vector4[] tangents,
          Color[] colors,
          BoneWeight[] boneWeights,
          List<Vector2>[] uvs2D,
          List<Vector3>[] uvs3D,
          List<Vector4>[] uvs4D,
          Matrix4x4[] bindposes,
          BlendShape[] blendShapes)
        {
            if (vertices == null)
                throw new ArgumentNullException(nameof(vertices));
            if (indices == null)
                throw new ArgumentNullException(nameof(indices));
            Mesh mesh = new Mesh();
            int length = indices.Length;
            if (bindposes != null && bindposes.Length != 0)
                mesh.bindposes = bindposes;
            mesh.subMeshCount = length;
            mesh.vertices = vertices;
            if (normals != null && normals.Length != 0)
                mesh.normals = normals;
            if (tangents != null && tangents.Length != 0)
                mesh.tangents = tangents;
            if (colors != null && colors.Length != 0)
                mesh.colors = colors;
            if (boneWeights != null && boneWeights.Length != 0)
                mesh.boneWeights = boneWeights;
            if (uvs2D != null)
            {
                for (int index = 0; index < uvs2D.Length; ++index)
                {
                    if (uvs2D[index] != null && uvs2D[index].Count > 0)
                        mesh.SetUVs(index, uvs2D[index]);
                }
            }
            if (uvs3D != null)
            {
                for (int index = 0; index < uvs3D.Length; ++index)
                {
                    if (uvs3D[index] != null && uvs3D[index].Count > 0)
                        mesh.SetUVs(index, uvs3D[index]);
                }
            }
            if (uvs4D != null)
            {
                for (int index = 0; index < uvs4D.Length; ++index)
                {
                    if (uvs4D[index] != null && uvs4D[index].Count > 0)
                        mesh.SetUVs(index, uvs4D[index]);
                }
            }
            if (blendShapes != null)
                MeshUtils.ApplyMeshBlendShapes(mesh, blendShapes);
            for (int index1 = 0; index1 < length; ++index1)
            {
                int[] index2 = indices[index1];
                mesh.SetTriangles(index2, index1, false);
            }
            mesh.RecalculateBounds();
            return mesh;
        }

        public static BlendShape[] GetMeshBlendShapes(Mesh mesh)
        {
            int length = null != mesh ? mesh.vertexCount : throw new ArgumentNullException(nameof(mesh));
            int blendShapeCount = mesh.blendShapeCount;
            if (blendShapeCount == 0)
                return (BlendShape[])null;
            BlendShape[] meshBlendShapes = new BlendShape[blendShapeCount];
            for (int index1 = 0; index1 < blendShapeCount; ++index1)
            {
                string blendShapeName = mesh.GetBlendShapeName(index1);
                int blendShapeFrameCount = mesh.GetBlendShapeFrameCount(index1);
                BlendShapeFrame[] frames = new BlendShapeFrame[blendShapeFrameCount];
                for (int index2 = 0; index2 < blendShapeFrameCount; ++index2)
                {
                    float shapeFrameWeight = mesh.GetBlendShapeFrameWeight(index1, index2);
                    Vector3[] deltaVertices = new Vector3[length];
                    Vector3[] deltaNormals = new Vector3[length];
                    Vector3[] deltaTangents = new Vector3[length];
                    mesh.GetBlendShapeFrameVertices(index1, index2, deltaVertices, deltaNormals, deltaTangents);
                    frames[index2] = new BlendShapeFrame(shapeFrameWeight, deltaVertices, deltaNormals, deltaTangents);
                }
                meshBlendShapes[index1] = new BlendShape(blendShapeName, frames);
            }
            return meshBlendShapes;
        }

        public static void ApplyMeshBlendShapes(Mesh mesh, BlendShape[] blendShapes)
        {
            if (null == mesh)
                throw new ArgumentNullException(nameof(mesh));
            mesh.ClearBlendShapes();
            if (blendShapes == null || blendShapes.Length == 0)
                return;
            for (int index1 = 0; index1 < blendShapes.Length; ++index1)
            {
                string shapeName = blendShapes[index1].ShapeName;
                BlendShapeFrame[] frames = blendShapes[index1].Frames;
                if (frames != null)
                {
                    for (int index2 = 0; index2 < frames.Length; ++index2)
                        mesh.AddBlendShapeFrame(shapeName, frames[index2].FrameWeight, frames[index2].DeltaVertices, frames[index2].DeltaNormals, frames[index2].DeltaTangents);
                }
            }
        }

        public static List<Vector4>[] GetMeshUVs(Mesh mesh)
        {
            if (null == mesh)
                throw new ArgumentNullException(nameof(mesh));
            List<Vector4>[] meshUvs = new List<Vector4>[MeshUtils.UVChannelCount];
            for (int channel = 0; channel < MeshUtils.UVChannelCount; ++channel)
                meshUvs[channel] = MeshUtils.GetMeshUVs(mesh, channel);
            return meshUvs;
        }

        public static List<Vector4> GetMeshUVs(Mesh mesh, int channel)
        {
            if (null == mesh)
                throw new ArgumentNullException(nameof(mesh));
            if (channel < 0 || channel >= MeshUtils.UVChannelCount)
                throw new ArgumentOutOfRangeException(nameof(channel));
            List<Vector4> meshUvs = new List<Vector4>(mesh.vertexCount);
            mesh.GetUVs(channel, meshUvs);
            return meshUvs;
        }

        public static int GetUsedUVComponents(List<Vector4> uvs)
        {
            if (uvs == null || uvs.Count == 0)
                return 0;
            int usedUvComponents = 0;
            foreach (Vector4 uv in uvs)
            {
                if (usedUvComponents < 1 && (double)uv.x != 0.0)
                    usedUvComponents = 1;
                if (usedUvComponents < 2 && (double)uv.y != 0.0)
                    usedUvComponents = 2;
                if (usedUvComponents < 3 && (double)uv.z != 0.0)
                    usedUvComponents = 3;
                if (usedUvComponents < 4 && (double)uv.w != 0.0)
                {
                    usedUvComponents = 4;
                    break;
                }
            }
            return usedUvComponents;
        }

        public static Vector2[] ConvertUVsTo2D(List<Vector4> uvs)
        {
            if (uvs == null)
                return (Vector2[])null;
            Vector2[] vector2Array = new Vector2[uvs.Count];
            for (int index = 0; index < vector2Array.Length; ++index)
            {
                Vector4 uv = uvs[index];
                vector2Array[index] = new Vector2(uv.x, uv.y);
            }
            return vector2Array;
        }

        public static Vector3[] ConvertUVsTo3D(List<Vector4> uvs)
        {
            if (uvs == null)
                return (Vector3[])null;
            Vector3[] vector3Array = new Vector3[uvs.Count];
            for (int index = 0; index < vector3Array.Length; ++index)
            {
                Vector4 uv = uvs[index];
                vector3Array[index] = new Vector3(uv.x, uv.y, uv.z);
            }
            return vector3Array;
        }

        private static void GetIndexMinMax(int[] indices, out int minIndex, out int maxIndex)
        {
            if (indices == null || indices.Length == 0)
            {
                minIndex = maxIndex = 0;
            }
            else
            {
                minIndex = int.MaxValue;
                maxIndex = int.MinValue;
                for (int index = 0; index < indices.Length; ++index)
                {
                    if (indices[index] < minIndex)
                        minIndex = indices[index];
                    if (indices[index] > maxIndex)
                        maxIndex = indices[index];
                }
            }
        }
    }
}
